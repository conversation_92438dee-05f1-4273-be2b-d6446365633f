/**
 * 发布管理工具
 * 一键发布流程和相关功能
 */

import { softwareDb as db } from '@/lib/software-db-connection'
import { software, softwareVersionHistory, softwareAnnouncements } from '@/lib/software-schema'
import { eq, desc, and } from 'drizzle-orm'
import { updateLatestVersion, getVersionType, isValidVersion } from '@/lib/version-manager'

/**
 * 一键发布接口
 */
export interface ReleaseRequest {
  // 版本信息
  version: string
  releaseDate?: string
  releaseNotes: string
  releaseNotesEn?: string
  
  // 下载链接
  downloadLinks?: {
    official?: string
    quark?: string
    pan123?: string
    baidu?: string
    thunder?: string
    backup?: string[]
  }
  
  // 版本属性
  isStable?: boolean
  isBeta?: boolean
  isPrerelease?: boolean
  fileSize?: string
  fileSizeBytes?: number
  fileHash?: string
  changelogCategory?: any
  
  // 软件信息更新
  updateSoftwareInfo?: {
    description?: string
    descriptionEn?: string
    officialWebsite?: string
    category?: string
    tags?: string[]
    systemRequirements?: any
  }
  
  // 公告信息
  createAnnouncement?: {
    title: string
    titleEn?: string
    content: string
    contentEn?: string
    type?: string
    priority?: 'low' | 'normal' | 'high' | 'urgent'
  }
  
  // 发布选项
  options?: {
    autoUpdateCurrentVersion?: boolean
    publishImmediately?: boolean
    notifyUsers?: boolean
    createBackup?: boolean
  }
}

/**
 * 发布结果接口
 */
export interface ReleaseResult {
  success: boolean
  message: string
  data?: {
    versionId?: number
    softwareUpdated?: boolean
    announcementId?: number
    backupCreated?: boolean
    notifications?: {
      sent: number
      failed: number
    }
  }
  errors?: string[]
}

/**
 * 一键发布功能
 */
export async function oneClickRelease(
  softwareId: number,
  releaseData: ReleaseRequest
): Promise<ReleaseResult> {
  const errors: string[] = []
  const result: ReleaseResult = {
    success: false,
    message: '',
    data: {},
    errors: []
  }

  try {
    // 1. 验证输入数据
    const validation = validateReleaseData(releaseData)
    if (!validation.valid) {
      return {
        success: false,
        message: '发布数据验证失败',
        errors: validation.errors
      }
    }

    // 2. 验证软件是否存在
    const [existingSoftware] = await db
      .select()
      .from(software)
      .where(eq(software.id, softwareId))
      .limit(1)

    if (!existingSoftware) {
      return {
        success: false,
        message: `软件 ID ${softwareId} 不存在`
      }
    }

    // 3. 检查版本是否已存在
    const [existingVersion] = await db
      .select()
      .from(softwareVersionHistory)
      .where(
        and(
          eq(softwareVersionHistory.softwareId, softwareId),
          eq(softwareVersionHistory.version, releaseData.version)
        )
      )
      .limit(1)

    if (existingVersion) {
      return {
        success: false,
        message: `版本 ${releaseData.version} 已存在`
      }
    }

    // 4. 可选：创建备份
    if (releaseData.options?.createBackup) {
      try {
        await createVersionBackup(softwareId, existingSoftware.currentVersion)
        result.data!.backupCreated = true
      } catch (error) {
        errors.push(`创建备份失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    // 5. 创建新版本记录
    const [newVersion] = await db
      .insert(softwareVersionHistory)
      .values({
        softwareId: softwareId,
        version: releaseData.version,
        releaseDate: releaseData.releaseDate ? new Date(releaseData.releaseDate) : new Date(),
        releaseNotes: releaseData.releaseNotes,
        releaseNotesEn: releaseData.releaseNotesEn,
        downloadLinks: releaseData.downloadLinks || {},
        fileSize: releaseData.fileSize,
        fileSizeBytes: releaseData.fileSizeBytes,
        fileHash: releaseData.fileHash,
        isStable: releaseData.isStable ?? true,
        isBeta: releaseData.isBeta ?? false,
        isPrerelease: releaseData.isPrerelease ?? false,
        versionType: getVersionType(releaseData.version),
        changelogCategory: releaseData.changelogCategory,
        metadata: {
          releaseMethod: 'oneClick',
          releaseTimestamp: new Date().toISOString()
        }
      })
      .returning()

    result.data!.versionId = newVersion.id

    // 6. 更新软件信息
    if (releaseData.updateSoftwareInfo || releaseData.options?.autoUpdateCurrentVersion !== false) {
      try {
        const updateData: any = {
          updatedAt: new Date()
        }

        // 自动更新当前版本
        if (releaseData.options?.autoUpdateCurrentVersion !== false) {
          updateData.currentVersion = releaseData.version
        }

        // 更新其他软件信息
        if (releaseData.updateSoftwareInfo) {
          Object.assign(updateData, releaseData.updateSoftwareInfo)
        }

        await db
          .update(software)
          .set(updateData)
          .where(eq(software.id, softwareId))

        result.data!.softwareUpdated = true
      } catch (error) {
        errors.push(`更新软件信息失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    // 7. 创建发布公告
    if (releaseData.createAnnouncement) {
      try {
        const [announcement] = await db
          .insert(softwareAnnouncements)
          .values({
            softwareId: softwareId,
            title: releaseData.createAnnouncement.title,
            titleEn: releaseData.createAnnouncement.titleEn,
            content: releaseData.createAnnouncement.content,
            contentEn: releaseData.createAnnouncement.contentEn,
            type: releaseData.createAnnouncement.type || 'release',
            priority: releaseData.createAnnouncement.priority || 'normal',
            version: releaseData.version,
            isPublished: releaseData.options?.publishImmediately ?? true,
            publishedAt: releaseData.options?.publishImmediately !== false ? new Date() : null
          })
          .returning()

        result.data!.announcementId = announcement.id
      } catch (error) {
        errors.push(`创建公告失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    // 8. 可选：发送通知（这里只是模拟，实际需要集成通知服务）
    if (releaseData.options?.notifyUsers) {
      try {
        const notificationResult = await sendReleaseNotifications(softwareId, releaseData.version)
        result.data!.notifications = notificationResult
      } catch (error) {
        errors.push(`发送通知失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    // 9. 设置结果
    result.success = true
    result.message = `版本 ${releaseData.version} 发布成功`
    result.errors = errors.length > 0 ? errors : undefined

    return result

  } catch (error) {
    console.error('一键发布失败:', error)
    return {
      success: false,
      message: `发布失败: ${error instanceof Error ? error.message : String(error)}`,
      errors: [...errors, error instanceof Error ? error.message : String(error)]
    }
  }
}

/**
 * 验证发布数据
 */
function validateReleaseData(data: ReleaseRequest): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // 必填字段检查
  if (!data.version) {
    errors.push('版本号不能为空')
  } else if (!isValidVersion(data.version)) {
    errors.push('版本号格式不正确')
  }

  if (!data.releaseNotes) {
    errors.push('发布说明不能为空')
  }

  // 公告数据检查
  if (data.createAnnouncement) {
    if (!data.createAnnouncement.title) {
      errors.push('公告标题不能为空')
    }
    if (!data.createAnnouncement.content) {
      errors.push('公告内容不能为空')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 创建版本备份
 */
async function createVersionBackup(softwareId: number, currentVersion: string): Promise<void> {
  await db
    .insert(softwareVersionHistory)
    .values({
      softwareId: softwareId,
      version: `${currentVersion}-backup-${Date.now()}`,
      releaseDate: new Date(),
      releaseNotes: `发布前的备份版本 (原版本: ${currentVersion})`,
      releaseNotesEn: `Backup version before release (original: ${currentVersion})`,
      downloadLinks: {},
      isStable: false,
      isBeta: false,
      isPrerelease: true,
      versionType: 'backup' as any,
      metadata: {
        isBackup: true,
        originalVersion: currentVersion,
        backupReason: 'Pre-release backup',
        backupDate: new Date().toISOString()
      }
    })
}

/**
 * 发送发布通知（模拟实现）
 */
async function sendReleaseNotifications(
  softwareId: number, 
  version: string
): Promise<{ sent: number; failed: number }> {
  // 这里是模拟实现，实际应该集成真实的通知服务
  // 比如邮件、短信、推送通知等
  
  try {
    // 模拟发送通知的逻辑
    await new Promise(resolve => setTimeout(resolve, 100))
    
    return {
      sent: 1, // 模拟发送成功数量
      failed: 0
    }
  } catch (error) {
    return {
      sent: 0,
      failed: 1
    }
  }
}

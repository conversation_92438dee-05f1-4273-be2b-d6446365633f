import { NextRequest } from 'next/server'
import { corsResponse, handleOptions } from '@/lib/cors'
import { softwareDb as db } from '@/lib/software-db-connection'
import { software, softwareVersionHistory, softwareAnnouncements, downloadStats } from '@/lib/software-schema'
import { eq, desc, and, count, sql, or } from 'drizzle-orm'
import { getVersionStats, getLatestVersion } from '@/lib/version-manager'

// OPTIONS 处理
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('Origin')
  const userAgent = request.headers.get('User-Agent')
  return handleOptions(origin, userAgent)
}

// GET /app/software/enhanced-query - 增强的综合查询接口
export async function GET(request: NextRequest) {
  const origin = request.headers.get('origin')
  const userAgent = request.headers.get('user-agent')

  try {
    const url = new URL(request.url)
    const queryType = url.searchParams.get('type') || 'overview'
    const softwareId = url.searchParams.get('softwareId')
    const softwareName = url.searchParams.get('softwareName')
    const includeStats = url.searchParams.get('includeStats') === 'true'
    const includeVersions = url.searchParams.get('includeVersions') === 'true'
    const includeAnnouncements = url.searchParams.get('includeAnnouncements') === 'true'
    const includeDownloadStats = url.searchParams.get('includeDownloadStats') === 'true'
    const limit = parseInt(url.searchParams.get('limit') || '10')

    switch (queryType) {
      case 'overview':
        return await handleOverviewQuery(origin, userAgent, {
          softwareId: softwareId ? parseInt(softwareId) : undefined,
          softwareName,
          includeStats,
          includeVersions,
          includeAnnouncements,
          includeDownloadStats,
          limit
        })

      case 'dashboard':
        return await handleDashboardQuery(origin, userAgent)

      case 'detailed':
        if (!softwareId && !softwareName) {
          return corsResponse({
            success: false,
            error: '详细查询需要提供软件ID或软件名称'
          }, { status: 400 }, origin, userAgent)
        }
        return await handleDetailedQuery(origin, userAgent, {
          softwareId: softwareId ? parseInt(softwareId) : undefined,
          softwareName
        })

      default:
        return corsResponse({
          success: false,
          error: `不支持的查询类型: ${queryType}`
        }, { status: 400 }, origin, userAgent)
    }

  } catch (error) {
    console.error('增强查询失败:', error)
    return corsResponse({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 }, origin, userAgent)
  }
}

/**
 * 处理概览查询
 */
async function handleOverviewQuery(
  origin: string | null,
  userAgent: string | null,
  options: {
    softwareId?: number
    softwareName?: string
    includeStats: boolean
    includeVersions: boolean
    includeAnnouncements: boolean
    includeDownloadStats: boolean
    limit: number
  }
) {
  try {
    let softwareQuery = db.select().from(software)

    // 构建查询条件
    if (options.softwareId) {
      softwareQuery = softwareQuery.where(eq(software.id, options.softwareId))
    } else if (options.softwareName) {
      const decodedName = decodeURIComponent(options.softwareName)
      softwareQuery = softwareQuery.where(
        or(
          eq(software.name, decodedName),
          eq(software.nameEn, decodedName)
        )
      )
    } else {
      softwareQuery = softwareQuery.where(eq(software.isActive, true))
    }

    const softwareList = await softwareQuery.limit(options.limit)

    // 为每个软件添加额外信息
    const enhancedSoftwareList = await Promise.all(
      softwareList.map(async (sw) => {
        const enhanced: any = { ...sw }

        // 添加版本统计
        if (options.includeStats) {
          enhanced.versionStats = await getVersionStats(sw.id)
        }

        // 添加最新版本信息
        if (options.includeVersions) {
          const latestVersions = await db
            .select()
            .from(softwareVersionHistory)
            .where(eq(softwareVersionHistory.softwareId, sw.id))
            .orderBy(desc(softwareVersionHistory.releaseDate))
            .limit(5)
          
          enhanced.recentVersions = latestVersions
          enhanced.latestStableVersion = await getLatestVersion(sw.id)
        }

        // 添加最新公告
        if (options.includeAnnouncements) {
          const announcements = await db
            .select()
            .from(softwareAnnouncements)
            .where(
              and(
                eq(softwareAnnouncements.softwareId, sw.id),
                eq(softwareAnnouncements.isPublished, true)
              )
            )
            .orderBy(desc(softwareAnnouncements.publishedAt))
            .limit(3)
          
          enhanced.recentAnnouncements = announcements
        }

        // 添加下载统计
        if (options.includeDownloadStats) {
          try {
            const downloadStatsData = await db
              .select({
                downloadSource: downloadStats.downloadSource,
                downloadCount: downloadStats.downloadCount,
                lastDownloadAt: downloadStats.lastDownloadAt
              })
              .from(downloadStats)
              .where(eq(downloadStats.softwareId, sw.id))
            
            enhanced.downloadStats = downloadStatsData
          } catch (error) {
            // 如果下载统计表不存在，忽略错误
            enhanced.downloadStats = []
          }
        }

        return enhanced
      })
    )

    return corsResponse({
      success: true,
      data: {
        software: enhancedSoftwareList,
        meta: {
          total: enhancedSoftwareList.length,
          includeStats: options.includeStats,
          includeVersions: options.includeVersions,
          includeAnnouncements: options.includeAnnouncements,
          includeDownloadStats: options.includeDownloadStats
        }
      }
    }, undefined, origin, userAgent)

  } catch (error) {
    console.error('概览查询失败:', error)
    throw error
  }
}

/**
 * 处理仪表板查询
 */
async function handleDashboardQuery(origin: string | null, userAgent: string | null) {
  try {
    // 获取总体统计
    const totalSoftware = await db
      .select({ count: count() })
      .from(software)
      .where(eq(software.isActive, true))

    const totalVersions = await db
      .select({ count: count() })
      .from(softwareVersionHistory)

    // 获取最近更新的软件
    const recentlyUpdated = await db
      .select()
      .from(software)
      .where(eq(software.isActive, true))
      .orderBy(desc(software.updatedAt))
      .limit(10)

    // 获取最新发布的版本
    const recentVersions = await db
      .select({
        id: softwareVersionHistory.id,
        softwareId: softwareVersionHistory.softwareId,
        version: softwareVersionHistory.version,
        releaseDate: softwareVersionHistory.releaseDate,
        isStable: softwareVersionHistory.isStable,
        softwareName: software.name
      })
      .from(softwareVersionHistory)
      .leftJoin(software, eq(softwareVersionHistory.softwareId, software.id))
      .orderBy(desc(softwareVersionHistory.releaseDate))
      .limit(10)

    // 获取分类统计
    const categoryStats = await db
      .select({
        category: software.category,
        count: count()
      })
      .from(software)
      .where(eq(software.isActive, true))
      .groupBy(software.category)

    return corsResponse({
      success: true,
      data: {
        overview: {
          totalSoftware: totalSoftware[0].count,
          totalVersions: totalVersions[0].count,
          categoriesCount: categoryStats.length
        },
        recentlyUpdated,
        recentVersions,
        categoryStats,
        generatedAt: new Date().toISOString()
      }
    }, undefined, origin, userAgent)

  } catch (error) {
    console.error('仪表板查询失败:', error)
    throw error
  }
}

/**
 * 处理详细查询
 */
async function handleDetailedQuery(
  origin: string | null,
  userAgent: string | null,
  options: {
    softwareId?: number
    softwareName?: string
  }
) {
  try {
    // 获取软件基本信息
    let softwareQuery = db.select().from(software)
    
    if (options.softwareId) {
      softwareQuery = softwareQuery.where(eq(software.id, options.softwareId))
    } else if (options.softwareName) {
      const decodedName = decodeURIComponent(options.softwareName)
      softwareQuery = softwareQuery.where(
        or(
          eq(software.name, decodedName),
          eq(software.nameEn, decodedName)
        )
      )
    }

    const [softwareInfo] = await softwareQuery.limit(1)

    if (!softwareInfo) {
      return corsResponse({
        success: false,
        error: '未找到指定的软件'
      }, { status: 404 }, origin, userAgent)
    }

    // 获取完整的版本历史
    const versionHistory = await db
      .select()
      .from(softwareVersionHistory)
      .where(eq(softwareVersionHistory.softwareId, softwareInfo.id))
      .orderBy(desc(softwareVersionHistory.releaseDate))

    // 获取版本统计
    const versionStats = await getVersionStats(softwareInfo.id)

    // 获取所有公告
    const announcements = await db
      .select()
      .from(softwareAnnouncements)
      .where(eq(softwareAnnouncements.softwareId, softwareInfo.id))
      .orderBy(desc(softwareAnnouncements.publishedAt))

    // 获取下载统计
    let downloadStatsData = []
    try {
      downloadStatsData = await db
        .select()
        .from(downloadStats)
        .where(eq(downloadStats.softwareId, softwareInfo.id))
    } catch (error) {
      // 如果下载统计表不存在，忽略错误
    }

    return corsResponse({
      success: true,
      data: {
        software: softwareInfo,
        versionHistory,
        versionStats,
        announcements,
        downloadStats: downloadStatsData,
        meta: {
          totalVersions: versionHistory.length,
          totalAnnouncements: announcements.length,
          latestVersion: versionHistory[0]?.version || softwareInfo.currentVersion,
          queryTime: new Date().toISOString()
        }
      }
    }, undefined, origin, userAgent)

  } catch (error) {
    console.error('详细查询失败:', error)
    throw error
  }
}

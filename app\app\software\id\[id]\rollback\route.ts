import { NextRequest } from 'next/server'
import { corsResponse, handleOptions, validateApiKeyWithExpiration } from '@/lib/cors'
import { rollbackToVersion } from '@/lib/version-manager'

// OPTIONS 处理
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('Origin')
  const userAgent = request.headers.get('User-Agent')
  return handleOptions(origin, userAgent)
}

// POST /app/software/id/[id]/rollback - 版本回滚
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const origin = request.headers.get('origin')
  const userAgent = request.headers.get('user-agent')

  try {
    // API Key 验证（写操作需要认证）
    if (process.env.ENABLE_API_KEY_AUTH === 'true') {
      const apiKeyValidation = validateApiKeyWithExpiration(request)
      if (!apiKeyValidation.isValid) {
        return corsResponse({
          success: false,
          error: apiKeyValidation.error || 'Invalid or missing API Key'
        }, { status: 401 }, origin, userAgent)
      }
    }

    const { id } = params

    if (!id) {
      return corsResponse({
        success: false,
        error: '软件ID参数缺失'
      }, { status: 400 }, origin, userAgent)
    }

    const softwareId = parseInt(id)
    if (isNaN(softwareId)) {
      return corsResponse({
        success: false,
        error: '无效的软件ID格式'
      }, { status: 400 }, origin, userAgent)
    }

    // 解析请求体
    const body = await request.json()
    const { 
      targetVersion, 
      createBackup = true, 
      updateDownloadUrl = false, 
      reason 
    } = body

    if (!targetVersion) {
      return corsResponse({
        success: false,
        error: '目标版本号不能为空'
      }, { status: 400 }, origin, userAgent)
    }

    // 执行版本回滚
    const result = await rollbackToVersion(softwareId, targetVersion, {
      createBackup,
      updateDownloadUrl,
      reason
    })

    if (!result.success) {
      return corsResponse({
        success: false,
        error: result.message
      }, { status: 400 }, origin, userAgent)
    }

    return corsResponse({
      success: true,
      message: result.message,
      data: {
        oldVersion: result.oldVersion,
        newVersion: result.newVersion,
        backupCreated: result.backupCreated
      }
    }, undefined, origin, userAgent)

  } catch (error) {
    console.error('版本回滚失败:', error)
    return corsResponse({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 }, origin, userAgent)
  }
}

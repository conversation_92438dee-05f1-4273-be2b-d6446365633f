import { NextRequest } from 'next/server'
import { corsResponse, handleOptions, validateApiKeyWithExpiration } from '@/lib/cors'
import { checkDataConsistency, updateLatestVersion } from '@/lib/version-manager'

// OPTIONS 处理
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('Origin')
  const userAgent = request.headers.get('User-Agent')
  return handleOptions(origin, userAgent)
}

// GET /app/software/consistency-check - 数据一致性检查
export async function GET(request: NextRequest) {
  const origin = request.headers.get('origin')
  const userAgent = request.headers.get('user-agent')

  try {
    const url = new URL(request.url)
    const softwareId = url.searchParams.get('softwareId')
    const autoFix = url.searchParams.get('autoFix') === 'true'

    // 执行一致性检查
    const checkResult = await checkDataConsistency(
      softwareId ? parseInt(softwareId) : undefined
    )

    if (!checkResult.success) {
      return corsResponse({
        success: false,
        error: '一致性检查失败'
      }, { status: 500 }, origin, userAgent)
    }

    // 如果启用自动修复且有高严重性问题
    let fixResults: any[] = []
    if (autoFix && checkResult.summary.highSeverity > 0) {
      for (const issue of checkResult.issues) {
        if (issue.severity === 'high' && issue.issue.includes('落后于最新稳定版本')) {
          try {
            const updateResult = await updateLatestVersion(issue.softwareId)
            fixResults.push({
              softwareId: issue.softwareId,
              softwareName: issue.softwareName,
              fixed: updateResult.success,
              message: updateResult.message
            })
          } catch (error) {
            fixResults.push({
              softwareId: issue.softwareId,
              softwareName: issue.softwareName,
              fixed: false,
              message: `修复失败: ${error instanceof Error ? error.message : String(error)}`
            })
          }
        }
      }
    }

    return corsResponse({
      success: true,
      data: {
        checkResult,
        autoFixResults: fixResults.length > 0 ? fixResults : undefined
      }
    }, undefined, origin, userAgent)

  } catch (error) {
    console.error('数据一致性检查失败:', error)
    return corsResponse({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 }, origin, userAgent)
  }
}

// POST /app/software/consistency-check - 修复数据一致性问题
export async function POST(request: NextRequest) {
  const origin = request.headers.get('origin')
  const userAgent = request.headers.get('user-agent')

  try {
    // API Key 验证（写操作需要认证）
    if (process.env.ENABLE_API_KEY_AUTH === 'true') {
      const apiKeyValidation = validateApiKeyWithExpiration(request)
      if (!apiKeyValidation.isValid) {
        return corsResponse({
          success: false,
          error: apiKeyValidation.error || 'Invalid or missing API Key'
        }, { status: 401 }, origin, userAgent)
      }
    }

    const body = await request.json()
    const { action, softwareIds } = body

    if (!action) {
      return corsResponse({
        success: false,
        error: '操作类型不能为空'
      }, { status: 400 }, origin, userAgent)
    }

    let results: any[] = []

    switch (action) {
      case 'fixVersionMismatch':
        // 修复版本不匹配问题
        if (!softwareIds || !Array.isArray(softwareIds)) {
          return corsResponse({
            success: false,
            error: '软件ID列表不能为空'
          }, { status: 400 }, origin, userAgent)
        }

        for (const softwareId of softwareIds) {
          try {
            const updateResult = await updateLatestVersion(parseInt(softwareId))
            results.push({
              softwareId: parseInt(softwareId),
              success: updateResult.success,
              message: updateResult.message,
              updated: updateResult.updated,
              oldVersion: updateResult.oldVersion,
              newVersion: updateResult.newVersion
            })
          } catch (error) {
            results.push({
              softwareId: parseInt(softwareId),
              success: false,
              message: `修复失败: ${error instanceof Error ? error.message : String(error)}`
            })
          }
        }
        break

      default:
        return corsResponse({
          success: false,
          error: `不支持的操作类型: ${action}`
        }, { status: 400 }, origin, userAgent)
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    return corsResponse({
      success: true,
      message: `修复完成：${successCount} 个成功，${failureCount} 个失败`,
      data: {
        results,
        summary: {
          total: results.length,
          success: successCount,
          failed: failureCount
        }
      }
    }, undefined, origin, userAgent)

  } catch (error) {
    console.error('修复数据一致性问题失败:', error)
    return corsResponse({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 }, origin, userAgent)
  }
}

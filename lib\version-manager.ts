/**
 * 版本管理工具
 * 自动化版本检测和管理功能
 */

import { softwareDb as db } from '@/lib/software-db-connection'
import { software, softwareVersionHistory, softwareAnnouncements } from '@/lib/software-schema'
import { eq, desc, and, or, count, sql } from 'drizzle-orm'

/**
 * 版本比较函数
 * 支持语义化版本号比较 (如: 1.0.0, 1.0.1, 2.0.0)
 */
export function compareVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)
  
  const maxLength = Math.max(v1Parts.length, v2Parts.length)
  
  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    
    if (v1Part > v2Part) return 1
    if (v1Part < v2Part) return -1
  }
  
  return 0
}

/**
 * 获取软件的最新版本号
 */
export async function getLatestVersion(softwareId: number): Promise<string | null> {
  try {
    const versions = await db
      .select({ version: softwareVersionHistory.version })
      .from(softwareVersionHistory)
      .where(
        and(
          eq(softwareVersionHistory.softwareId, softwareId),
          eq(softwareVersionHistory.isStable, true)
        )
      )
      .orderBy(desc(softwareVersionHistory.releaseDate))

    if (versions.length === 0) return null

    // 按版本号排序，获取最新版本
    const sortedVersions = versions
      .map(v => v.version)
      .sort((a, b) => compareVersions(b, a))

    return sortedVersions[0]
  } catch (error) {
    console.error('获取最新版本失败:', error)
    return null
  }
}

/**
 * 获取软件的最新版本记录（包含完整信息）
 */
export async function getLatestVersionRecord(softwareId: number) {
  try {
    const versions = await db
      .select()
      .from(softwareVersionHistory)
      .where(
        and(
          eq(softwareVersionHistory.softwareId, softwareId),
          eq(softwareVersionHistory.isStable, true)
        )
      )
      .orderBy(desc(softwareVersionHistory.releaseDate))

    if (versions.length === 0) return null

    // 按版本号排序，获取最新版本记录
    const sortedVersions = versions.sort((a, b) => compareVersions(b.version, a.version))
    return sortedVersions[0]
  } catch (error) {
    console.error('获取最新版本记录失败:', error)
    return null
  }
}

/**
 * 自动更新软件的最新版本号（增强版）
 */
export async function updateLatestVersion(
  softwareId: number,
  options: {
    updateDownloadUrl?: boolean;
    updateOfficialWebsite?: boolean;
    syncMetadata?: boolean;
  } = {}
): Promise<{
  success: boolean;
  updated: boolean;
  oldVersion?: string;
  newVersion?: string;
  message: string;
}> {
  try {
    const latestVersionRecord = await getLatestVersionRecord(softwareId)

    if (!latestVersionRecord) {
      return {
        success: false,
        updated: false,
        message: `软件 ${softwareId} 没有找到稳定版本`
      }
    }

    // 获取当前软件信息
    const [currentSoftware] = await db
      .select()
      .from(software)
      .where(eq(software.id, softwareId))

    if (!currentSoftware) {
      return {
        success: false,
        updated: false,
        message: `软件 ${softwareId} 不存在`
      }
    }

    // 检查是否需要更新
    const needsUpdate = compareVersions(latestVersionRecord.version, currentSoftware.currentVersion) > 0

    if (!needsUpdate) {
      return {
        success: true,
        updated: false,
        oldVersion: currentSoftware.currentVersion,
        newVersion: latestVersionRecord.version,
        message: '版本已是最新，无需更新'
      }
    }

    // 准备更新数据
    const updateData: any = {
      currentVersion: latestVersionRecord.version,
      updatedAt: new Date()
    }

    // 可选：同步下载链接
    if (options.updateDownloadUrl && latestVersionRecord.downloadLinks?.official) {
      updateData.officialWebsite = latestVersionRecord.downloadLinks.official
    }

    // 可选：同步元数据
    if (options.syncMetadata && latestVersionRecord.metadata) {
      updateData.metadata = {
        ...currentSoftware.metadata,
        ...latestVersionRecord.metadata,
        lastVersionSync: new Date().toISOString()
      }
    }

    // 执行更新
    await db
      .update(software)
      .set(updateData)
      .where(eq(software.id, softwareId))

    console.log(`软件 ${softwareId} 版本已更新：${currentSoftware.currentVersion} -> ${latestVersionRecord.version}`)

    return {
      success: true,
      updated: true,
      oldVersion: currentSoftware.currentVersion,
      newVersion: latestVersionRecord.version,
      message: '版本更新成功'
    }
  } catch (error) {
    console.error('更新最新版本失败:', error)
    return {
      success: false,
      updated: false,
      message: `更新失败: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * 批量更新所有软件的最新版本号
 */
export async function updateAllLatestVersions(): Promise<{
  updated: number;
  failed: number;
  total: number;
}> {
  try {
    const allSoftware = await db
      .select({ id: software.id, name: software.name })
      .from(software)
      .where(eq(software.isActive, true))

    let updated = 0
    let failed = 0

    for (const sw of allSoftware) {
      try {
        const success = await updateLatestVersion(sw.id)
        if (success) {
          updated++
        }
      } catch (error) {
        console.error(`更新软件 ${sw.name} (${sw.id}) 失败:`, error)
        failed++
      }
    }

    return {
      updated,
      failed,
      total: allSoftware.length
    }
  } catch (error) {
    console.error('批量更新版本失败:', error)
    throw error
  }
}

/**
 * 检查版本号格式是否有效
 */
export function isValidVersion(version: string): boolean {
  // 支持语义化版本号格式: x.y.z 或 x.y.z-beta.1 等
  const versionRegex = /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/
  return versionRegex.test(version)
}

/**
 * 获取版本类型
 */
export function getVersionType(version: string): 'release' | 'beta' | 'alpha' | 'rc' {
  const lowerVersion = version.toLowerCase()
  
  if (lowerVersion.includes('alpha')) return 'alpha'
  if (lowerVersion.includes('beta')) return 'beta'
  if (lowerVersion.includes('rc')) return 'rc'
  
  return 'release'
}

/**
 * 获取软件的版本历史统计
 */
export async function getVersionStats(softwareId: number) {
  try {
    const versions = await db
      .select()
      .from(softwareVersionHistory)
      .where(eq(softwareVersionHistory.softwareId, softwareId))
      .orderBy(desc(softwareVersionHistory.releaseDate))

    const stats = {
      total: versions.length,
      stable: versions.filter(v => v.isStable).length,
      beta: versions.filter(v => v.isBeta).length,
      prerelease: versions.filter(v => v.isPrerelease).length,
      latest: versions[0]?.version || null,
      oldest: versions[versions.length - 1]?.version || null,
      releaseFrequency: calculateReleaseFrequency(versions)
    }

    return stats
  } catch (error) {
    console.error('获取版本统计失败:', error)
    return null
  }
}

/**
 * 计算发布频率（天数）
 */
function calculateReleaseFrequency(versions: any[]): number | null {
  if (versions.length < 2) return null

  const dates = versions.map(v => new Date(v.releaseDate)).sort((a, b) => a.getTime() - b.getTime())
  const totalDays = (dates[dates.length - 1].getTime() - dates[0].getTime()) / (1000 * 60 * 60 * 24)
  
  return Math.round(totalDays / (versions.length - 1))
}

/**
 * 自动检测并建议版本号
 */
export async function suggestNextVersion(
  softwareId: number,
  changeType: 'major' | 'minor' | 'patch' = 'patch'
): Promise<string | null> {
  try {
    const latestVersion = await getLatestVersion(softwareId)

    if (!latestVersion) return '1.0.0'

    const parts = latestVersion.split('.').map(Number)

    switch (changeType) {
      case 'major':
        return `${parts[0] + 1}.0.0`
      case 'minor':
        return `${parts[0]}.${parts[1] + 1}.0`
      case 'patch':
        return `${parts[0]}.${parts[1]}.${parts[2] + 1}`
      default:
        return `${parts[0]}.${parts[1]}.${parts[2] + 1}`
    }
  } catch (error) {
    console.error('建议版本号失败:', error)
    return null
  }
}

/**
 * 版本回滚功能
 */
export async function rollbackToVersion(
  softwareId: number,
  targetVersion: string,
  options: {
    createBackup?: boolean;
    updateDownloadUrl?: boolean;
    reason?: string;
  } = {}
): Promise<{
  success: boolean;
  message: string;
  oldVersion?: string;
  newVersion?: string;
  backupCreated?: boolean;
}> {
  try {
    // 验证目标版本是否存在
    const [targetVersionRecord] = await db
      .select()
      .from(softwareVersionHistory)
      .where(
        and(
          eq(softwareVersionHistory.softwareId, softwareId),
          eq(softwareVersionHistory.version, targetVersion)
        )
      )
      .limit(1)

    if (!targetVersionRecord) {
      return {
        success: false,
        message: `目标版本 ${targetVersion} 不存在`
      }
    }

    // 获取当前软件信息
    const [currentSoftware] = await db
      .select()
      .from(software)
      .where(eq(software.id, softwareId))

    if (!currentSoftware) {
      return {
        success: false,
        message: `软件 ${softwareId} 不存在`
      }
    }

    const oldVersion = currentSoftware.currentVersion

    // 可选：创建备份记录
    let backupCreated = false
    if (options.createBackup) {
      try {
        await db
          .insert(softwareVersionHistory)
          .values({
            softwareId: softwareId,
            version: `${oldVersion}-backup-${Date.now()}`,
            releaseDate: new Date(),
            releaseNotes: `回滚前的备份版本 (原版本: ${oldVersion})`,
            releaseNotesEn: `Backup version before rollback (original: ${oldVersion})`,
            downloadLinks: {},
            isStable: false,
            isBeta: false,
            isPrerelease: true,
            versionType: 'backup' as any,
            metadata: {
              isBackup: true,
              originalVersion: oldVersion,
              rollbackReason: options.reason || 'Manual rollback',
              rollbackDate: new Date().toISOString()
            }
          })
        backupCreated = true
      } catch (error) {
        console.warn('创建备份失败:', error)
      }
    }

    // 准备更新数据
    const updateData: any = {
      currentVersion: targetVersion,
      updatedAt: new Date()
    }

    // 可选：同步下载链接
    if (options.updateDownloadUrl && targetVersionRecord.downloadLinks?.official) {
      updateData.officialWebsite = targetVersionRecord.downloadLinks.official
    }

    // 更新软件信息
    await db
      .update(software)
      .set(updateData)
      .where(eq(software.id, softwareId))

    console.log(`软件 ${softwareId} 已回滚：${oldVersion} -> ${targetVersion}`)

    return {
      success: true,
      message: '版本回滚成功',
      oldVersion,
      newVersion: targetVersion,
      backupCreated
    }
  } catch (error) {
    console.error('版本回滚失败:', error)
    return {
      success: false,
      message: `回滚失败: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * 数据一致性检查
 */
export async function checkDataConsistency(softwareId?: number): Promise<{
  success: boolean;
  issues: Array<{
    softwareId: number;
    softwareName: string;
    issue: string;
    severity: 'low' | 'medium' | 'high';
    suggestion: string;
  }>;
  summary: {
    totalChecked: number;
    issuesFound: number;
    highSeverity: number;
  };
}> {
  try {
    const issues: any[] = []

    // 确定要检查的软件范围
    const softwareToCheck = softwareId
      ? await db.select().from(software).where(eq(software.id, softwareId))
      : await db.select().from(software).where(eq(software.isActive, true))

    for (const sw of softwareToCheck) {
      // 检查1: 软件表中的currentVersion是否与版本历史表中的最新稳定版本一致
      const latestStableVersion = await getLatestVersion(sw.id)
      if (latestStableVersion && compareVersions(latestStableVersion, sw.currentVersion) > 0) {
        issues.push({
          softwareId: sw.id,
          softwareName: sw.name,
          issue: `当前版本 ${sw.currentVersion} 落后于最新稳定版本 ${latestStableVersion}`,
          severity: 'high' as const,
          suggestion: `建议更新currentVersion为 ${latestStableVersion}`
        })
      }

      // 检查2: 是否存在版本历史记录
      const versionCount = await db
        .select({ count: count() })
        .from(softwareVersionHistory)
        .where(eq(softwareVersionHistory.softwareId, sw.id))

      if (versionCount[0].count === 0) {
        issues.push({
          softwareId: sw.id,
          softwareName: sw.name,
          issue: '缺少版本历史记录',
          severity: 'medium' as const,
          suggestion: '建议为当前版本创建版本历史记录'
        })
      }

      // 检查3: 版本号格式验证
      if (!isValidVersion(sw.currentVersion)) {
        issues.push({
          softwareId: sw.id,
          softwareName: sw.name,
          issue: `版本号格式不规范: ${sw.currentVersion}`,
          severity: 'low' as const,
          suggestion: '建议使用语义化版本号格式 (如: 1.0.0)'
        })
      }
    }

    const summary = {
      totalChecked: softwareToCheck.length,
      issuesFound: issues.length,
      highSeverity: issues.filter(i => i.severity === 'high').length
    }

    return {
      success: true,
      issues,
      summary
    }
  } catch (error) {
    console.error('数据一致性检查失败:', error)
    return {
      success: false,
      issues: [],
      summary: { totalChecked: 0, issuesFound: 0, highSeverity: 0 }
    }
  }
}
